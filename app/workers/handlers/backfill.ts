import type { Gap } from '../../modules/backfill/entities/gap'
import { backfill } from '../../common/backfill'
import { raydium } from '../../common/raydium'

export interface BackfillGapPayload {
    start: bigint
    end: bigint
}

export interface BackfillEntityPayload {
    entity: Gap
}

export type BackfillPayload = BackfillGapPayload | BackfillEntityPayload

export async function handleBackfill(payload: BackfillPayload) {
    if ('start' in payload && 'end' in payload) {
        return backfill.handleGap(payload.start, payload.end, (transactions) => {
            raydium.handleTransactions(transactions)
        })
    }
}
