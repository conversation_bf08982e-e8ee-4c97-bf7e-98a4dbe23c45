import { initializeBackfill } from './common/backfill'
import { initializeRaydium } from './common/raydium'
import { initializeDatabase } from './core/database'
import { initializeGeyser } from './core/geyser'
import { logger } from './core/logger'
import { initializeWorkerPool } from './core/worker'
import 'reflect-metadata'

async function init() {
    await initializeDatabase()
    await initializeWorkerPool()
    await initializeGeyser()
    await initializeRaydium()
    await initializeBackfill()
}

init().catch((error) => {
    logger.forceExit(1, 'fatal', 'Failed to initialize application', error)
})
