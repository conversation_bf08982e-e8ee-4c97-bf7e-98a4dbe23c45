import { isMainThread } from 'node:worker_threads'
import { highlight } from '@kdt310722/logger'
import { notNullish } from '@kdt310722/utils/common'
import { tap } from '@kdt310722/utils/function'
import { format } from '@kdt310722/utils/number'
import { config } from '../config'
import { database } from '../core/database'
import { createChildLogger } from '../core/logger'
import { rpc } from '../core/rpc'
import { onGap } from '../core/slot'
import { workerPool } from '../core/worker'
import { Backfill } from '../modules/backfill/backfill'
import { RaydiumLaunchpad } from '../modules/raydium-launchpad/raydium-launchpad'
import { splitSlots } from '../utils/backfill'
import { raydium, waitForFirstReceivedSlot } from './raydium'

const logger = createChildLogger('common:backfill')

function handleGap(startSlot: bigint, endSlot: bigint) {
    logger.warn(`Gap found: ${highlight(startSlot)} -> ${highlight(endSlot)} (${highlight(format(endSlot - startSlot + 1n))} slots)`)

    const pools = config.backfill.maxPools ?? workerPool.size
    const gaps = splitSlots(pools, startSlot, endSlot, config.backfill.batchSize)

    for (const [start, end] of gaps) {
        workerPool.submitTask({ type: 'backfill', payload: { start, end } })
    }
}

if (isMainThread) {
    onGap((startSlot, endSlot) => handleGap(startSlot - 1n, endSlot + 1n))
}

export const backfill = new Backfill(database, rpc, RaydiumLaunchpad.getAccountFilter(), config.backfill)

export async function initializeBackfill() {
    const timer = tap(logger.createTimer(), () => logger.info('Initializing backfill...'))
    const startSlot = await raydium.getLastKnownSlot()
    const endSlot = await waitForFirstReceivedSlot()

    if (notNullish(startSlot)) {
        handleGap(startSlot, endSlot)
    }

    logger.stopTimer(timer, 'info', 'Backfill initialized!')
}
