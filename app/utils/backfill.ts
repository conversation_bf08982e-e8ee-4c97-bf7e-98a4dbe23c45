export function splitSlots(poolsCount: number, startSlot: bigint, endSlot: bigint, minSlotsPerPool: number) {
    const totalSlots = Number(endSlot - startSlot + 1n)
    const maxPools = Math.min(Math.ceil(totalSlots / minSlotsPerPool), poolsCount)
    const slotsPerPool = Math.max(Math.ceil(totalSlots / poolsCount), minSlotsPerPool)

    const result: Array<[start: bigint, end: bigint]> = []

    for (let i = 0; i < maxPools; i++) {
        const start = startSlot + BigInt(i * slotsPerPool)
        const end = BigInt(Math.min(Number(start) + slotsPerPool - 1, Number(endSlot)))

        result.push([start, end])
    }

    return result
}
