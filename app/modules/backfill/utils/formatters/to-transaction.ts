import type { SolanaTransaction, Transaction } from '../../../rpc-client/types'
import { signature } from '@solana/kit'
import { getTransactionInstructions } from '../../../rpc-client/utils/instructions'

export const toTransaction = (data: SolanaTransaction): Transaction => ({
    slot: data.slot,
    index: data.index,
    signature: signature(data.transaction.signatures[0]),
    blockTime: data.blockTime,
    instructions: getTransactionInstructions(data),
})
